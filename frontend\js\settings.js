/**
 * ReplyPal - Settings Management
 * Handles loading, saving, and managing settings
 */

// DOM Elements
const environmentSelect = document.getElementById('environment');
const currentApiUrlSpan = document.getElementById('current-api-url');
const testApiBtn = document.getElementById('test-api-btn');
const apiTestResult = document.getElementById('api-test-result');
const saveHistoryCheckbox = document.getElementById('save-history');
const useMockApiCheckbox = document.getElementById('use-mock-api');
const saveSettingsBtn = document.getElementById('save-settings-btn');
const apiSettingsHeading = document.querySelector('#settings .settings-container h3:nth-of-type(3)');
const apiSettingsFormGroup = document.querySelector('#settings .settings-container .form-group:nth-of-type(3)');
const toggleHotkeysBtn = document.getElementById('toggle-hotkeys-btn');
const additionalHotkeys = document.getElementById('additional-hotkeys');
const usageProgress = document.getElementById('usage-progress');
const remainingResponses = document.getElementById('remaining-responses');
const totalResponses = document.getElementById('total-responses');
const upgradeLink = document.getElementById('upgrade-link');
const hideFloatingIconGloballyCheckbox = document.getElementById('hide-floating-icon-globally');
const hiddenDomainsList = document.getElementById('hidden-domains-list');
const clearHiddenDomainsBtn = document.getElementById('clear-hidden-domains-btn');

// Configuration is now centralized in config.js
// Access via window.ReplyPalConfig

// Set up event listeners
document.addEventListener('DOMContentLoaded', async () => {
  // Initialize settings first to ensure proper migration
  await window.ReplyPalConfig.initializeSettings();

  // Call toggleApiSettingsVisibility on page load
  toggleApiSettingsVisibility();

  // Environment selection change
  if (environmentSelect) {
    environmentSelect.addEventListener('change', updateApiUrlDisplay);
  }

  // Test API button
  if (testApiBtn) {
    testApiBtn.addEventListener('click', testApiConnection);
  }

  // Save settings button
  if (saveSettingsBtn) {
    saveSettingsBtn.addEventListener('click', saveSettings);
  }

  // Toggle hotkeys button
  if (toggleHotkeysBtn && additionalHotkeys) {
    toggleHotkeysBtn.addEventListener('click', toggleHotkeysVisibility);
  }

  // Upgrade link
  if (upgradeLink) {
    upgradeLink.addEventListener('click', handleUpgradeClick);
  }

  // Clear hidden domains button
  if (clearHiddenDomainsBtn) {
    clearHiddenDomainsBtn.addEventListener('click', clearHiddenDomains);
  }

  // Load settings on page load
  loadSettings();

  // Fetch usage information
  fetchUsageInfo();
});

/**
 * Load settings from storage
 */
async function loadSettings() {
  try {
    const settings = await window.ReplyPalConfig.getCurrentSettings();

    // Set environment
    if (environmentSelect) {
      environmentSelect.value = settings.environment || 'prod';
      updateApiUrlDisplay();
    }

    // Set other settings
    if (saveHistoryCheckbox) {
      saveHistoryCheckbox.checked = settings.saveHistory !== false;
    }

    if (useMockApiCheckbox) {
      useMockApiCheckbox.checked = settings.useMockApi === true;
    }

    // Set floating icon settings
    if (hideFloatingIconGloballyCheckbox) {
      hideFloatingIconGloballyCheckbox.checked = settings.hideFloatingIconGlobally === true;
    }

    // Load hidden domains list
    loadHiddenDomainsList(settings.hiddenDomains || []);
  } catch (error) {
    console.error('Error loading settings:', error);
  }
}

/**
 * Toggle API settings visibility based on default environment
 * Hide API settings section when default environment is set to production
 */
function toggleApiSettingsVisibility() {
  if (!apiSettingsHeading || !apiSettingsFormGroup) return;

  const isProdEnvironment = window.ReplyPalConfig.DEFAULT_SETTINGS.environment === 'prod';

  // Hide or show API settings section based on default environment
  if (isProdEnvironment) {
    // Hide API settings in production environment
    apiSettingsHeading.style.display = 'none';
    apiSettingsFormGroup.style.display = 'none';
  } else {
    // Show API settings in non-production environments
    apiSettingsHeading.style.display = '';
    apiSettingsFormGroup.style.display = '';
  }
}

/**
 * Update API URL display based on selected environment
 */
function updateApiUrlDisplay() {
  if (currentApiUrlSpan && environmentSelect) {
    const environment = environmentSelect.value;
    currentApiUrlSpan.textContent = window.ReplyPalConfig.getApiUrl(environment);
  }
}

/**
 * Save settings to storage
 */
function saveSettings() {
  // Get current settings to preserve hidden domains
  chrome.storage.local.get('settings', (data) => {
    const currentSettings = data.settings || {};

    const settings = {
      environment: environmentSelect ? environmentSelect.value : 'prod',
      saveHistory: saveHistoryCheckbox ? saveHistoryCheckbox.checked : true,
      useMockApi: useMockApiCheckbox ? useMockApiCheckbox.checked : false,
      hideFloatingIconGlobally: hideFloatingIconGloballyCheckbox ? hideFloatingIconGloballyCheckbox.checked : false,
      hiddenDomains: currentSettings.hiddenDomains || []
    };

    chrome.storage.local.set({ settings: settings }, () => {
      showToast('Settings saved');
    });
  });
}

/**
 * Get settings from storage
 * @returns {Promise<Object>} Settings object
 */
function getSettings() {
  return window.ReplyPalConfig.getCurrentSettings();
}

/**
 * Get API URL based on environment
 * @param {string} environment - The environment (local, dev, prod)
 * @returns {string} The API URL for the specified environment
 */
function getApiUrlForEnvironment(environment) {
  return window.ReplyPalConfig.getApiUrl(environment);
}

/**
 * Test API connection
 */
async function testApiConnection() {
  if (!apiTestResult) return;

  // Get the API URL
  const environment = environmentSelect ? environmentSelect.value : 'local';
  const apiUrl = window.ReplyPalConfig.getApiUrl(environment);

  // Update the test result
  apiTestResult.textContent = "Testing connection...";
  apiTestResult.style.color = "var(--text-color)";

  try {
    // Test the ping endpoint
    const response = await fetch(`${apiUrl}/ping`, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'Origin': window.location.origin,
        'X-Requested-With': 'XMLHttpRequest'
      },
      mode: 'cors',
      credentials: 'omit'
    });

    if (response.ok) {
      const data = await response.json();
      apiTestResult.textContent = `Connection successful! API version: ${data.version}`;
      apiTestResult.style.color = "var(--success-color)";

      // Also test the generate endpoint with a simple request
      try {
        const generateResponse = await fetch(`${apiUrl}/generate`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'Origin': window.location.origin,
            'X-Requested-With': 'XMLHttpRequest'
          },
          mode: 'cors',
          credentials: 'omit',
          body: JSON.stringify({
            selected_text: "This is a test message.",
            user_intent: "Test the API connection",
            tone: "neutral",
            purpose: "reply"
          })
        });

        if (generateResponse.ok) {
          apiTestResult.textContent += "\nGenerate endpoint is also working!";
          console.log(`Settings: Generate endpoint test successful at ${apiUrl}/generate`);
        } else {
          apiTestResult.textContent += "\nGenerate endpoint test failed: " +
            generateResponse.status + " " + generateResponse.statusText;
          console.error(`Settings: Generate endpoint test failed at ${apiUrl}/generate`);
          console.error(`Settings: Status: ${generateResponse.status} ${generateResponse.statusText}`);
          console.error(`Settings: Response headers:`, Object.fromEntries(generateResponse.headers.entries()));
        }
      } catch (generateError) {
        apiTestResult.textContent += "\nGenerate endpoint test error: " + generateError.message;
        console.error(`Settings: Generate endpoint test error at ${apiUrl}/generate:`, generateError);
        console.error(`Settings: Error name: ${generateError.name}`);
        console.error(`Settings: Error message: ${generateError.message}`);
      }
    } else {
      apiTestResult.textContent = `Connection failed: ${response.status} ${response.statusText}`;
      apiTestResult.style.color = "var(--error-color)";

      // Log detailed error information
      console.error(`Settings: Ping endpoint failed at ${apiUrl}/ping`);
      console.error(`Settings: Status: ${response.status} ${response.statusText}`);
      console.error(`Settings: Response headers:`, Object.fromEntries(response.headers.entries()));

      // Try to get error response body
      response.text().then(errorBody => {
        console.error(`Settings: Error response body:`, errorBody);
      }).catch(e => {
        console.error(`Settings: Could not read error response body:`, e);
      });
    }
  } catch (error) {
    // Provide more detailed error information
    let errorMessage = error.message;

    // Check if it's a network error
    if (error.name === 'TypeError' && error.message.includes('Failed to fetch')) {
      errorMessage = 'Network error: Unable to connect to the API server. Please check if the server is running and accessible.';
    }

    // Check if it's a CORS error
    if (error.message.includes('CORS')) {
      errorMessage = 'CORS error: The API server is not allowing requests from the extension. Please check CORS settings on the server.';
    }

    apiTestResult.textContent = `Connection error: ${errorMessage}`;
    apiTestResult.style.color = "var(--error-color)";

    // Provide helpful guidance based on environment
    console.error(`Settings: API connection failed for environment '${environment}'`);
    console.error(`Settings: Target URL: ${apiUrl}`);
    console.error(`Settings: Error details:`, error);

    if (environment === 'local') {
      console.error('Settings: Local API connection failed. To start the FastAPI server:');
      console.error('Settings: 1. Open terminal in the ReplyPal directory');
      console.error('Settings: 2. Run: cd fastapi');
      console.error('Settings: 3. Run: .\\env\\scripts\\activate');
      console.error('Settings: 4. Run: python -m uvicorn main:app');
      console.error('Settings: 5. The server should start at http://localhost:8000');
    }
  }
}

/**
 * Toggle visibility of additional hotkeys
 */
function toggleHotkeysVisibility() {
  if (!toggleHotkeysBtn || !additionalHotkeys) return;

  // Toggle the 'hidden' class on the additional hotkeys container
  additionalHotkeys.classList.toggle('hidden');

  // Toggle the 'expanded' class on the button to rotate the icon
  toggleHotkeysBtn.classList.toggle('expanded');

  // Update the button title based on current state
  if (additionalHotkeys.classList.contains('hidden')) {
    toggleHotkeysBtn.title = 'Show all shortcuts';
  } else {
    toggleHotkeysBtn.title = 'Hide additional shortcuts';
  }
}

/**
 * Fetch usage information from the API and update the UI
 */
async function fetchUsageInfo() {
  // Declare variables at function level for error logging
  let environment = 'local';
  let apiUrl = '';

  try {
    // Get user data for authentication
    const userData = await getUserData();

    if (!userData || !userData.apiToken) {
      // User is not logged in, show default values
      console.log('Settings: No user data or API token found, showing default values');
      updateUsageUI(0, 5);
      return;
    }

    // Get settings for API URL using centralized configuration
    const settings = await getSettings();
    environment = settings.environment || window.ReplyPalConfig.DEFAULT_SETTINGS.environment;
    apiUrl = window.ReplyPalConfig.getApiUrl(environment);

    console.log(`Settings: Fetching usage info from ${apiUrl}/subscription/usage (environment: ${environment})`);

    // Add timeout and better error handling
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout

    try {
      // Fetch usage information
      const response = await fetch(`${apiUrl}/subscription/usage`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${userData.apiToken}`,
          'Content-Type': 'application/json'
        },
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        console.error(`Settings: Usage API returned error status: ${response.status} ${response.statusText}`);
        console.error(`Settings: Request URL: ${apiUrl}/subscription/usage`);
        console.error(`Settings: Environment: ${environment}`);
        console.error(`Settings: Response headers:`, Object.fromEntries(response.headers.entries()));

        // Try to get error details
        try {
          const errorData = await response.text();
          console.error('Settings: Error response body:', errorData);
        } catch (e) {
          console.error('Settings: Could not read error response body:', e);
        }

        // Show default values on API error
        updateUsageUI(0, 5);
        return;
      }

      const usageData = await response.json();
      console.log('Settings: Received usage data:', usageData);

      const dailyUsage = usageData.daily_usage || 0;
      const dailyLimit = usageData.daily_limit || 5;
      const remaining = Math.max(0, dailyLimit - dailyUsage);

      // Update UI with usage information
      updateUsageUI(remaining, dailyLimit);
    } catch (fetchError) {
      clearTimeout(timeoutId);
      throw fetchError;
    }
  } catch (error) {
    console.error('Settings: Error fetching usage information:', error);
    console.error(`Settings: Failed request URL: ${apiUrl}/subscription/usage`);
    console.error(`Settings: Environment: ${environment}`);
    console.error(`Settings: Error name: ${error.name}`);
    console.error(`Settings: Error message: ${error.message}`);

    // Provide more specific error information
    if (error.name === 'AbortError') {
      console.error('Settings: Request timed out after 10 seconds');
      console.error(`Settings: Check if the API server at ${apiUrl} is responding`);
    } else if (error.message.includes('Failed to fetch')) {
      console.error('Settings: Network error - API server may not be running or CORS issue');
      console.error(`Settings: Check if the API server is running at the configured URL: ${apiUrl}`);
      console.error(`Settings: Verify CORS settings allow requests from extension origin`);
    }

    // Show default values on error
    updateUsageUI(0, 5);
  }
}

/**
 * Update the usage UI with the provided values
 * @param {number} remaining - Remaining responses
 * @param {number} total - Total allowed responses
 */
function updateUsageUI(remaining, total) {
  if (remainingResponses) {
    remainingResponses.textContent = remaining;
  }

  if (totalResponses) {
    totalResponses.textContent = total;
  }

  if (usageProgress) {
    const percentage = total > 0 ? ((total - remaining) / total) * 100 : 0;
    usageProgress.style.width = `${percentage}%`;

    // Change color based on usage
    if (percentage > 80) {
      usageProgress.style.backgroundColor = '#ff9800'; // Warning color
    } else if (percentage > 95) {
      usageProgress.style.backgroundColor = '#f44336'; // Error color
    } else {
      usageProgress.style.backgroundColor = ''; // Default color
    }
  }
}

/**
 * Handle upgrade link click
 */
async function handleUpgradeClick(e) {
  e.preventDefault();

  try {
    // Get user data
    const userData = await getUserData();

    // Get settings for API URL using centralized configuration
    const settings = await getSettings();
    const environment = settings.environment || window.ReplyPalConfig.DEFAULT_SETTINGS.environment;

    // If user is logged in, pass login details to subscription page
    if (userData && userData.apiToken) {
      // Store user data in localStorage for the subscription page
      localStorage.setItem('replypal_user', JSON.stringify(userData));
      localStorage.setItem('replypal_environment', environment);
      // Mark that we're coming from the extension
      localStorage.setItem('replypal_from_extension', 'true');
    }

    // Open subscription page in a new tab
    window.open(window.ReplyPalConfig.getSubscriptionUrl(), '_blank');

    // Show toast notification
    showToast('Opening subscription page...', false);
  } catch (error) {
    console.error('Settings: Error handling upgrade click:', error);
    console.error(`Settings: Subscription URL: ${window.ReplyPalConfig.getSubscriptionUrl()}`);
    console.error(`Settings: Error name: ${error.name}`);
    console.error(`Settings: Error message: ${error.message}`);
    showToast('Error opening subscription page', true);
  }
}

/**
 * Get user data from storage
 */
function getUserData() {
  return new Promise((resolve) => {
    chrome.storage.local.get('user', (data) => {
      resolve(data.user || null);
    });
  });
}

/**
 * Load and display hidden domains list
 */
function loadHiddenDomainsList(hiddenDomains) {
  if (!hiddenDomainsList) return;

  // Clear existing list
  hiddenDomainsList.innerHTML = '';

  if (!hiddenDomains || hiddenDomains.length === 0) {
    // Show empty state
    const emptyDiv = document.createElement('div');
    emptyDiv.className = 'hidden-domains-empty';
    emptyDiv.textContent = 'No hidden domains';
    hiddenDomainsList.appendChild(emptyDiv);
    return;
  }

  // Add each domain
  hiddenDomains.forEach(domain => {
    const domainItem = document.createElement('div');
    domainItem.className = 'hidden-domain-item';

    domainItem.innerHTML = `
      <span class="domain-name">${domain}</span>
      <button class="remove-domain-btn" data-domain="${domain}" title="Remove domain">
        <i class="fa-solid fa-times"></i>
      </button>
    `;

    // Add remove event listener
    const removeBtn = domainItem.querySelector('.remove-domain-btn');
    removeBtn.addEventListener('click', () => removeDomain(domain));

    hiddenDomainsList.appendChild(domainItem);
  });
}

/**
 * Remove a domain from hidden domains list
 */
function removeDomain(domain) {
  chrome.storage.local.get('settings', (data) => {
    const settings = data.settings || {};
    const hiddenDomains = settings.hiddenDomains || [];

    // Remove the domain
    const updatedDomains = hiddenDomains.filter(d => d !== domain);
    settings.hiddenDomains = updatedDomains;

    chrome.storage.local.set({ settings: settings }, () => {
      loadHiddenDomainsList(updatedDomains);
      showToast(`Removed ${domain} from hidden domains`);
    });
  });
}

/**
 * Clear all hidden domains
 */
function clearHiddenDomains() {
  if (confirm('Are you sure you want to clear all hidden domains? The ReplyPal floating icon will appear on all websites again.')) {
    chrome.storage.local.get('settings', (data) => {
      const settings = data.settings || {};
      settings.hiddenDomains = [];

      chrome.storage.local.set({ settings: settings }, () => {
        loadHiddenDomainsList([]);
        showToast('All hidden domains cleared');
      });
    });
  }
}

/**
 * Show a toast notification
 * @param {string} message - The message to display
 * @param {boolean} isError - Whether this is an error message
 */
function showToast(message, isError = false) {
  const toast = document.getElementById('toast');
  if (!toast) return;

  // Clear any existing timeout
  if (toast.timeoutId) {
    clearTimeout(toast.timeoutId);
  }

  // Set message and class
  toast.textContent = message;
  toast.className = 'toast';
  if (isError) {
    toast.classList.add('error');
  }

  // Show toast
  setTimeout(() => {
    toast.classList.add('show');
  }, 10);

  // Hide toast after 3 seconds
  toast.timeoutId = setTimeout(() => {
    toast.classList.remove('show');
  }, 3000);
}

// ReplyPal Settings API for backward compatibility
const ReplyPalSettings = {
  getApiUrl: function() {
    const environment = environmentSelect ? environmentSelect.value : window.ReplyPalConfig.DEFAULT_SETTINGS.environment;
    return window.ReplyPalConfig.getApiUrl(environment);
  },

  setEnvironment: function(environment) {
    if (environmentSelect && window.ReplyPalConfig.API_URLS[environment]) {
      environmentSelect.value = environment;
      updateApiUrlDisplay();
    }
  }
};
