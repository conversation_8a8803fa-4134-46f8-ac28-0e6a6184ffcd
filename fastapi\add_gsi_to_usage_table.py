#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to add GSI to the usage records table
"""

import boto3
import time
from botocore.exceptions import ClientError

# Initialize DynamoDB client with profile
session = boto3.Session(profile_name='sumathy_aws')
dynamodb_client = session.client('dynamodb')

def add_gsi_to_usage_table():
    """Add UserIdIndex GSI to the usage records table"""
    table_name = "replypal-usage-records"
    
    try:
        print(f"Adding UserIdIndex GSI to {table_name}...")
        
        response = dynamodb_client.update_table(
            TableName=table_name,
            AttributeDefinitions=[
                {
                    'AttributeName': 'user_id',
                    'AttributeType': 'S'
                }
            ],
            GlobalSecondaryIndexUpdates=[
                {
                    'Create': {
                        'IndexName': 'UserIdIndex',
                        'KeySchema': [
                            {
                                'AttributeName': 'user_id',
                                'KeyType': 'HASH'
                            }
                        ],
                        'Projection': {
                            'ProjectionType': 'ALL'
                        },
                        'ProvisionedThroughput': {
                            'ReadCapacityUnits': 5,
                            'WriteCapacityUnits': 5
                        }
                    }
                }
            ]
        )
        
        print(f"GSI creation initiated. Response: {response['TableDescription']['TableStatus']}")
        
        # Wait for the GSI to be created
        print("Waiting for GSI to be created...")
        waiter = dynamodb_client.get_waiter('table_exists')
        waiter.wait(TableName=table_name)
        
        # Check the status
        while True:
            response = dynamodb_client.describe_table(TableName=table_name)
            table_status = response['Table']['TableStatus']
            
            if 'GlobalSecondaryIndexes' in response['Table']:
                gsi_status = response['Table']['GlobalSecondaryIndexes'][0]['IndexStatus']
                print(f"Table Status: {table_status}, GSI Status: {gsi_status}")
                
                if gsi_status == 'ACTIVE':
                    print("GSI created successfully!")
                    break
                elif gsi_status in ['CREATING', 'UPDATING']:
                    print("GSI still being created, waiting...")
                    time.sleep(10)
                else:
                    print(f"Unexpected GSI status: {gsi_status}")
                    break
            else:
                print("No GSI found yet, waiting...")
                time.sleep(10)
                
    except ClientError as e:
        if e.response['Error']['Code'] == 'ResourceInUseException':
            print("Table is currently being updated. Please wait and try again.")
        elif e.response['Error']['Code'] == 'ValidationException':
            if 'already exists' in str(e):
                print("GSI already exists!")
            else:
                print(f"Validation error: {str(e)}")
        else:
            print(f"Error adding GSI: {str(e)}")

if __name__ == "__main__":
    add_gsi_to_usage_table()
