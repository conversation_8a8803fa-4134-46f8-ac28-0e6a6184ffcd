#!/usr/bin/env python3
"""
Test script to verify usage tracking is working correctly
"""

import requests
import json
import time
from datetime import datetime

# API configuration
API_BASE_URL = "http://127.0.0.1:8000"

def create_test_token():
    """Create a test JWT token"""
    url = f"{API_BASE_URL}/auth/token"
    data = {
        "uid": "test_user_123",
        "email": "<EMAIL>",
        "name": "Test User",
        "picture": "https://example.com/avatar.jpg"
    }

    try:
        response = requests.post(url, json=data)
        if response.status_code == 200:
            result = response.json()
            return result.get("access_token")
        else:
            print(f"Failed to create token: {response.status_code} - {response.text}")
            return None
    except Exception as e:
        print(f"Error creating token: {str(e)}")
        return None

def test_usage_endpoint(token):
    """Test the usage endpoint to see current usage"""
    url = f"{API_BASE_URL}/subscription/usage"
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }

    try:
        response = requests.get(url, headers=headers)
        print(f"Usage endpoint status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"Usage data: {json.dumps(data, indent=2)}")
        else:
            print(f"Error response: {response.text}")
    except Exception as e:
        print(f"Error calling usage endpoint: {str(e)}")

def test_generate_endpoint(token):
    """Test the generate endpoint to create usage records"""
    url = f"{API_BASE_URL}/generate"
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }

    data = {
        "selected_text": "Hello world",
        "user_intent": "reply",
        "tone": "professional",
        "purpose": "respond"
    }

    try:
        print(f"Making request to {url}")
        response = requests.post(url, headers=headers, json=data)
        print(f"Generate endpoint status: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"Generated response: {result.get('response', 'No response')[:100]}...")
        else:
            print(f"Error response: {response.text}")
    except Exception as e:
        print(f"Error calling generate endpoint: {str(e)}")

def main():
    """Main test function"""
    print("=" * 50)
    print("TESTING USAGE TRACKING")
    print("=" * 50)

    # Create test token
    print("Creating test token...")
    token = create_test_token()
    if not token:
        print("Failed to create test token. Exiting.")
        return

    print(f"Token created successfully: {token[:50]}...")

    # Test 1: Check current usage
    print("\n1. Checking current usage...")
    test_usage_endpoint(token)

    # Test 2: Make a generate request
    print("\n2. Making a generate request...")
    test_generate_endpoint(token)

    # Test 3: Check usage again to see if it increased
    print("\n3. Checking usage after request...")
    time.sleep(1)  # Give it a moment to process
    test_usage_endpoint(token)

if __name__ == "__main__":
    main()
