#!/usr/bin/env python3
"""
Script to check usage records in DynamoDB
"""

import sys
import os
from datetime import datetime

# Add the parent directory to the path so we can import from the app
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database import usage_records_table, datetime_to_iso

def check_usage_records():
    """Check all usage records in the database"""
    if usage_records_table is None:
        print("ERROR: usage_records_table is None. Database not initialized.")
        return
    
    try:
        # Scan all records
        response = usage_records_table.scan()
        items = response.get("Items", [])
        
        print(f"Found {len(items)} usage records:")
        print("=" * 50)
        
        for item in items:
            user_id = item.get("user_id", "NO_USER_ID")
            timestamp = item.get("timestamp", "NO_TIMESTAMP")
            request_type = item.get("request_type", "NO_TYPE")
            tokens_used = item.get("tokens_used", "NO_TOKENS")
            record_id = item.get("id", "NO_ID")
            
            print(f"ID: {record_id}")
            print(f"User: {user_id}")
            print(f"Timestamp: {timestamp}")
            print(f"Type: {request_type}")
            print(f"Tokens: {tokens_used}")
            print("-" * 30)
            
        # Check today's records
        today_start = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
        today_start_iso = datetime_to_iso(today_start)
        
        today_records = [item for item in items if item.get("timestamp") and item["timestamp"] >= today_start_iso]
        print(f"\nToday's records (since {today_start_iso}): {len(today_records)}")
        
        for item in today_records:
            user_id = item.get("user_id", "NO_USER_ID")
            timestamp = item.get("timestamp", "NO_TIMESTAMP")
            request_type = item.get("request_type", "NO_TYPE")
            print(f"  {user_id} - {timestamp} - {request_type}")
            
    except Exception as e:
        print(f"Error checking usage records: {str(e)}")
        print(f"Exception type: {type(e).__name__}")

if __name__ == "__main__":
    check_usage_records()
