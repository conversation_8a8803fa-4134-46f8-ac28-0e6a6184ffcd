#!/usr/bin/env python3
"""
Script to check DynamoDB table structure
"""

import boto3
import json
from botocore.exceptions import ClientError

# Initialize DynamoDB client with profile
session = boto3.Session(profile_name='sumathy_aws')
dynamodb_client = session.client('dynamodb')

def check_table_structure(table_name):
    """Check the structure of a DynamoDB table"""
    try:
        response = dynamodb_client.describe_table(TableName=table_name)
        table_info = response['Table']
        
        print(f"Table: {table_name}")
        print(f"Status: {table_info['TableStatus']}")
        print(f"Key Schema: {json.dumps(table_info['KeySchema'], indent=2)}")
        
        if 'GlobalSecondaryIndexes' in table_info:
            print(f"Global Secondary Indexes:")
            for gsi in table_info['GlobalSecondaryIndexes']:
                print(f"  - Index Name: {gsi['IndexName']}")
                print(f"    Key Schema: {json.dumps(gsi['KeySchema'], indent=4)}")
                print(f"    Status: {gsi['IndexStatus']}")
        else:
            print("No Global Secondary Indexes found")
            
        print("-" * 50)
        
    except ClientError as e:
        print(f"Error checking table {table_name}: {str(e)}")

def main():
    """Main function"""
    tables = [
        "replypal-customers",
        "replypal-subscriptions", 
        "replypal-user-subscriptions",
        "replypal-usage-records"
    ]
    
    for table in tables:
        check_table_structure(table)

if __name__ == "__main__":
    main()
