"""
Subscription middleware for ReplyPal API
Checks subscription status and usage limits
"""

import logging
import os
from datetime import datetime
from fastapi import Request, status

from ..models import UsageRecord, SubscriptionStatus
from .. import database as db
from ..auth.middleware import CustomJ<PERSON>NResponse, convert_decimal_in_dict
from .settings import (
    AUTO_RENEWAL_TOKEN_THRESHOLD,
    AUTO_RENEWAL_TIME_THRESHOLD_DAYS,
    STRIPE_BASIC_PRICE_ID,
    TIER_LIMITS
)

# Configure logging
logger = logging.getLogger(__name__)


async def check_subscription_status(user_id: str) -> bool:
    """Check if user has an active subscription"""
    logger.info(f"Checking subscription for user: {user_id}")

    try:
        subscription = await db.get_user_subscription(user_id)

        if not subscription:
            logger.info(f"No subscription found for user: {user_id}, creating free tier")
            # Create a free subscription for the user if they don't have one
            from .stripe_service import TIER_LIMITS
            from ..models import UserSubscription, SubscriptionTier

            try:
                # Create user subscription with free tier
                user_subscription = UserSubscription(
                    user_id=user_id,
                    stripe_customer_id="free_tier_" + user_id,  # Placeholder for free tier
                    tier=SubscriptionTier.FREE,
                    status=SubscriptionStatus.ACTIVE,
                    usage_limits=TIER_LIMITS[SubscriptionTier.FREE]
                )
                await db.create_user_subscription(user_subscription)
                logger.info(f"Created free subscription for user: {user_id}")
                return True
            except Exception as e:
                logger.error(f"Error creating free subscription for user {user_id}: {str(e)}")
                # Return True anyway to allow access when DB operations fail
                logger.warning(f"Allowing access for user {user_id} despite DB error")
                return True

        # Check if subscription is active
        valid_statuses = [
            SubscriptionStatus.ACTIVE.value,
            SubscriptionStatus.TRIALING.value
        ]

        is_active = subscription.get("status") in valid_statuses
        logger.info(f"Subscription status for user {user_id}: {subscription.get('status')} (active: {is_active})")
        return is_active
    except Exception as e:
        logger.error(f"Error checking subscription status for user {user_id}: {str(e)}")
        # Return True to allow access when DB operations fail
        logger.warning(f"Allowing access for user {user_id} despite DB error")
        return True


async def check_usage_limits(user_id: str) -> bool:
    """Check if user has exceeded usage limits"""
    try:
        logger.info(f"Checking usage limits for user {user_id}")

        subscription = await db.get_user_subscription(user_id)
        if not subscription:
            logger.warning(f"No subscription found for user {user_id} when checking usage limits")
            # Create a free subscription for the user if they don't have one
            from ..models import UserSubscription, SubscriptionTier

            try:
                # Create user subscription with free tier
                user_subscription = UserSubscription(
                    user_id=user_id,
                    stripe_customer_id="free_tier_" + user_id,  # Placeholder for free tier
                    tier=SubscriptionTier.FREE,
                    status=SubscriptionStatus.ACTIVE,
                    usage_limits=TIER_LIMITS[SubscriptionTier.FREE]
                )
                await db.create_user_subscription(user_subscription)
                logger.info(f"Created free subscription for user: {user_id}")
                # Use the new subscription's limits
                subscription = await db.get_user_subscription(user_id)
                if not subscription:
                    # If still no subscription, use default free tier limit
                    limit = 5  # Default free tier limit
                    logger.warning(f"Using default free tier limit ({limit}) for user {user_id}")
                else:
                    limit = subscription.get("usage_limits", {}).get("requests_per_day", 5)
                    logger.info(f"Using limit from newly created subscription: {limit}")
            except Exception as e:
                logger.error(f"Error creating free subscription for user {user_id}: {str(e)}")
                # Use default free tier limit
                limit = 5  # Default free tier limit
                logger.warning(f"Using default free tier limit ({limit}) for user {user_id}")
        else:
            # Get limit from existing subscription
            limit = subscription.get("usage_limits", {}).get("requests_per_day", 5)
            logger.info(f"Using limit from existing subscription: {limit}")

        # Get daily usage count
        daily_usage = await db.get_daily_usage_count(user_id)

        # Check if user has exceeded daily request limit
        logger.info(f"User {user_id} usage: {daily_usage}/{limit}")

        # If usage is close to limit, log a warning
        if daily_usage >= limit - 2 and daily_usage < limit:
            logger.warning(f"User {user_id} is approaching their daily limit: {daily_usage}/{limit}")

        # Check if user has reached or exceeded their limit
        if daily_usage >= limit:
            logger.warning(f"User {user_id} has reached/exceeded their daily limit: {daily_usage}/{limit}")
            within_limits = False
        else:
            within_limits = True

        logger.info(f"User {user_id} is {'within' if within_limits else 'exceeding'} usage limits")
        return within_limits
    except Exception as e:
        logger.error(f"Error checking usage limits for user {user_id}: {str(e)}")
        logger.error(f"Exception type: {type(e).__name__}")
        # Allow access when DB operations fail to provide better user experience
        # Usage will still be tracked in the main endpoint if possible
        logger.warning(f"Allowing access for user {user_id} despite DB error when checking usage limits")
        return True


async def check_and_auto_renew(user_id: str, subscription: dict, tokens_used: int) -> bool:
    """
    Check if a user's subscription needs auto-renewal based on token usage or time
    Returns True if renewal was triggered, False otherwise
    """
    # Only process for BASIC tier subscriptions
    from ..models import SubscriptionTier
    if subscription.get("tier") != SubscriptionTier.BASIC.value:
        return False

    # Get current total tokens used
    current_total_tokens = subscription.get("total_tokens_used", 0)
    new_total_tokens = current_total_tokens + tokens_used

    # Get last renewal date
    from datetime import timedelta
    last_renewal_date = subscription.get("last_renewal_date")
    current_time = datetime.now()

    # Check if we need to renew based on token usage threshold
    token_threshold_reached = new_total_tokens >= AUTO_RENEWAL_TOKEN_THRESHOLD

    # Check if we need to renew based on time (1 month since last renewal)
    time_threshold_reached = False
    if last_renewal_date:
        # Convert ISO string to datetime if needed
        if isinstance(last_renewal_date, str):
            try:
                from dateutil import parser
                last_renewal_date = parser.parse(last_renewal_date)
            except Exception as e:
                logger.error(f"Error parsing last_renewal_date: {str(e)}")
                last_renewal_date = None

        if last_renewal_date:
            renewal_threshold_date = current_time - timedelta(days=AUTO_RENEWAL_TIME_THRESHOLD_DAYS)
            time_threshold_reached = last_renewal_date <= renewal_threshold_date

    # If either threshold is reached, trigger renewal
    if token_threshold_reached or time_threshold_reached:
        logger.info(f"Auto-renewal triggered for user {user_id}. "
                   f"Token threshold reached: {token_threshold_reached} ({new_total_tokens}/{AUTO_RENEWAL_TOKEN_THRESHOLD}), "
                   f"Time threshold reached: {time_threshold_reached} (threshold: {AUTO_RENEWAL_TIME_THRESHOLD_DAYS} days)")

        try:
            # Import here to avoid circular imports
            from . import stripe_service

            # Get the Stripe subscription ID
            stripe_subscription_id = subscription.get("stripe_subscription_id")
            if not stripe_subscription_id:
                logger.warning(f"No Stripe subscription ID found for user {user_id}, cannot auto-renew")
                return False

            # Get the Stripe customer ID
            stripe_customer_id = subscription.get("stripe_customer_id")
            if not stripe_customer_id:
                logger.warning(f"No Stripe customer ID found for user {user_id}, cannot auto-renew")
                return False

            # Trigger renewal using the Stripe API
            # Use the price ID from settings
            price_id = STRIPE_BASIC_PRICE_ID

            # Create a new subscription or update the existing one
            await stripe_service.create_subscription(
                user_id=user_id,
                customer_id=stripe_customer_id,
                price_id=price_id
            )

            # Reset the total tokens used counter and update the last renewal date
            await db.update_user_subscription(
                user_id=user_id,
                update_data={
                    "total_tokens_used": 0,
                    "last_renewal_date": current_time
                }
            )

            logger.info(f"Successfully auto-renewed subscription for user {user_id}")
            return True
        except Exception as e:
            logger.error(f"Error auto-renewing subscription for user {user_id}: {str(e)}")
            return False

    # If no renewal was needed, update the total tokens used
    try:
        await db.update_user_subscription(
            user_id=user_id,
            update_data={"total_tokens_used": new_total_tokens}
        )
        logger.info(f"Updated total tokens used for user {user_id}: {current_total_tokens} -> {new_total_tokens}")
    except Exception as e:
        logger.error(f"Error updating total tokens used for user {user_id}: {str(e)}")

    return False


async def check_all_subscriptions_for_renewal() -> None:
    """
    Check all subscriptions for auto-renewal based on time threshold
    This function is intended to be run as a scheduled task
    """
    logger.info("Checking all subscriptions for auto-renewal")

    try:
        # Get all user subscriptions
        subscriptions = await db.get_all_user_subscriptions()

        # Filter for BASIC tier subscriptions
        from ..models import SubscriptionTier
        basic_subscriptions = [
            sub for sub in subscriptions
            if sub.get("tier") == SubscriptionTier.BASIC.value
        ]

        logger.info(f"Found {len(basic_subscriptions)} BASIC tier subscriptions to check")

        # Check each subscription for renewal
        for subscription in basic_subscriptions:
            user_id = subscription.get("user_id")
            if not user_id:
                logger.warning(f"Subscription missing user_id: {subscription}")
                continue

            # Check if subscription needs renewal based on time
            from datetime import timedelta
            last_renewal_date = subscription.get("last_renewal_date")
            current_time = datetime.now()

            # Skip if no last renewal date
            if not last_renewal_date:
                logger.warning(f"No last_renewal_date for user {user_id}, skipping")
                continue

            # Convert ISO string to datetime if needed
            if isinstance(last_renewal_date, str):
                try:
                    from dateutil import parser
                    last_renewal_date = parser.parse(last_renewal_date)
                except Exception as e:
                    logger.error(f"Error parsing last_renewal_date for user {user_id}: {str(e)}")
                    continue

            # Check if renewal time threshold has passed since last renewal
            renewal_threshold_date = current_time - timedelta(days=AUTO_RENEWAL_TIME_THRESHOLD_DAYS)
            if last_renewal_date <= renewal_threshold_date:
                logger.info(f"Time-based auto-renewal needed for user {user_id}. Last renewal: {last_renewal_date}")

                # Trigger renewal with 0 tokens used (just for the time-based check)
                await check_and_auto_renew(user_id, subscription, 0)
            else:
                days_left = (last_renewal_date + timedelta(days=AUTO_RENEWAL_TIME_THRESHOLD_DAYS) - current_time).days
                logger.info(f"No time-based renewal needed for user {user_id}. Days until renewal: {days_left}/{AUTO_RENEWAL_TIME_THRESHOLD_DAYS}")

    except Exception as e:
        logger.error(f"Error checking subscriptions for renewal: {str(e)}")
        logger.error(f"Exception type: {type(e).__name__}")


async def record_usage(user_id: str, request_type: str, tokens_used: int) -> tuple[bool, bool]:
    """
    Record API usage and return a tuple containing:
    - success status (bool): Whether the usage was successfully recorded
    - limit_exceeded (bool): Whether the user has exceeded their daily limit
    """
    try:
        # First check current usage to ensure we're not exceeding limits
        subscription = await db.get_user_subscription(user_id)
        limit = 5  # Default limit

        if subscription:
            # Get the user's limit
            limit = subscription.get("usage_limits", {}).get("requests_per_day", 5)
            logger.info(f"User {user_id} has subscription with limit: {limit}")

            # Check if we need to auto-renew the subscription
            if subscription.get("tier") == "basic":
                await check_and_auto_renew(user_id, subscription, tokens_used)
        else:
            logger.warning(f"No subscription found for user {user_id}, using default limit: {limit}")

        # Get current usage
        current_usage = await db.get_daily_usage_count(user_id)

        # Log current usage status
        logger.info(f"Current usage for user {user_id}: {current_usage}/{limit}")

        # Check if user would exceed their limit after this request
        # Allow usage exactly at limit (e.g., 5/5 is allowed, but 6/5 is not)
        would_exceed_limit = current_usage >= limit
        if would_exceed_limit:
            logger.warning(f"User {user_id} would exceed their daily limit: {current_usage}/{limit}")
            # Don't record the usage and return that the limit is exceeded
            return False, True

        # Create the usage record with current timestamp
        current_time = datetime.now()
        usage_record = UsageRecord(
            user_id=user_id,
            request_type=request_type,
            tokens_used=tokens_used,
            timestamp=current_time,
            metadata={
                "recorded_at": current_time.isoformat(),
                "day": current_time.strftime("%Y-%m-%d")
            }
        )

        # Log the attempt to create a usage record
        logger.info(f"Attempting to record usage for user {user_id}: {tokens_used} tokens for {request_type}")
        logger.info(f"Record timestamp: {current_time.isoformat()}")

        # Create the usage record
        result = await db.create_usage_record(usage_record)

        if result:
            logger.info(f"Successfully recorded usage for user {user_id}: {tokens_used} tokens for {request_type}")
            logger.info(f"Record ID: {result.get('id')}")

            # Get updated usage count for verification
            updated_usage = await db.get_daily_usage_count(user_id)
            logger.info(f"Updated usage for user {user_id}: {updated_usage}/{limit}")

            # Check if the updated count is as expected
            if updated_usage <= current_usage:
                logger.warning(f"Usage count did not increase as expected: {current_usage} -> {updated_usage}")

            # Since we checked the limit before recording, the user should be within limits
            return True, False
        else:
            logger.warning(f"Failed to record usage for user {user_id}: No result returned from create_usage_record")
            return False, False
    except Exception as e:
        logger.error(f"Error recording usage for user {user_id}: {str(e)}")
        # Log the exception type for debugging
        logger.error(f"Exception type: {type(e).__name__}")
        # Continue without recording usage, but don't assume limit is exceeded
        # This provides better user experience when there are temporary DB issues
        logger.warning(f"Failed to record usage for user {user_id} due to error, but allowing access")
        return False, False


async def get_token_limit(user_id: str) -> int:
    """Get token limit for user"""
    try:
        subscription = await db.get_user_subscription(user_id)
        if not subscription:
            # If no subscription found, check if we need to create one
            is_active = await check_subscription_status(user_id)
            if is_active:
                # Subscription was created, get it again
                subscription = await db.get_user_subscription(user_id)
            else:
                # Default to a reasonable limit if we can't create a subscription
                logger.info(f"Using default token limit (500) for user {user_id}")
                return int(500)

        limit = subscription.get("usage_limits", {}).get("max_tokens", 500)
        # Ensure the limit is an integer to avoid DeepSeek API errors
        limit = int(limit)
        logger.info(f"Token limit for user {user_id}: {limit}")
        return limit
    except Exception as e:
        logger.error(f"Error getting token limit for user {user_id}: {str(e)}")
        # Return default limit when DB operations fail
        logger.warning(f"Using default token limit (500) for user {user_id} due to DB error")
        return int(500)


class SubscriptionMiddleware:
    """Middleware to check subscription status and usage limits"""

    def __init__(
        self,
        app,
        excluded_paths: list = None
    ):
        self.app = app
        self.excluded_paths = excluded_paths or [
            "/docs",
            "/redoc",
            "/openapi.json",
            "/ping",
            "/auth/token",
            "/subscription/webhook",
            "/subscription/create-checkout-session",
            "/subscription/customer-portal"
        ]

    async def __call__(self, scope, receive, send):
        if scope["type"] != "http":
            # If not an HTTP request, just pass through
            await self.app(scope, receive, send)
            return

        # Create a Starlette Request object
        request = Request(scope, receive=receive)

        # Note: We're not using call_next in this middleware, but keeping the code
        # as a reference for future use if needed
        # async def call_next(req):
        #     # This is a bit of a hack, but it allows us to intercept the response
        #     response_sent = False
        #
        #     async def send_wrapper(message):
        #         nonlocal response_sent
        #         if message["type"] == "http.response.start":
        #             response_sent = True
        #         await send(message)
        #
        #     await self.app(scope, receive, send_wrapper)
        #     return Response(content=b"", status_code=200)  # Dummy response, won't be used

        # Skip middleware for excluded paths
        if any(request.url.path.startswith(path) for path in self.excluded_paths):
            await self.app(scope, receive, send)
            return

        # Get user from request
        try:
            user = request.state.user
            user_id = user.uid
            logger.info(f"Subscription middleware found user in request state: {user_id}")
        except AttributeError:
            # If no user in request state, continue to next middleware
            # Authentication middleware will handle unauthorized requests
            logger.warning(f"No user found in request state for path: {request.url.path}")
            await self.app(scope, receive, send)
            return

        # Check subscription status
        logger.info(f"Checking subscription status for user: {user_id}")
        is_active = await check_subscription_status(user_id)
        logger.info(f"Subscription status for user {user_id}: {'active' if is_active else 'inactive'}")

        if not is_active:
            logger.warning(f"Inactive subscription for user: {user_id}")
            response_content = {
                "detail": "Your subscription is not active. Please update your payment information."
            }
            # Convert any Decimal values in the response content
            response_content = convert_decimal_in_dict(response_content)

            response = CustomJSONResponse(
                status_code=status.HTTP_402_PAYMENT_REQUIRED,
                content=response_content,
                media_type="application/json"
            )
            await response(scope, receive, send)
            return

        # Check usage limits
        within_limits = await check_usage_limits(user_id)
        if not within_limits:
            response_content = {
                "detail": "You have exceeded your daily usage limit. Please upgrade your subscription."
            }
            # Convert any Decimal values in the response content
            response_content = convert_decimal_in_dict(response_content)

            response = CustomJSONResponse(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                content=response_content,
                media_type="application/json"
            )
            await response(scope, receive, send)
            return

        # Continue with the request
        await self.app(scope, receive, send)

        # Note: We can't easily record usage after the response in this ASGI middleware pattern
        # Consider moving usage recording to the route handlers or using a background task
