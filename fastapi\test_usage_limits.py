#!/usr/bin/env python3
"""
Test script to verify usage limits functionality
"""

import requests
import json
import time
from datetime import datetime

# API base URL
API_BASE = "http://127.0.0.1:8000"

def create_test_token():
    """Create a test JWT token"""
    token_request = {
        "uid": "test-user-123",
        "email": "<EMAIL>",
        "name": "Test User"
    }
    
    response = requests.post(f"{API_BASE}/auth/token", json=token_request)
    if response.status_code == 200:
        return response.json()["access_token"]
    else:
        print(f"Failed to create token: {response.status_code} - {response.text}")
        return None

def test_generate_endpoint(token, test_name=""):
    """Test the generate endpoint"""
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    data = {
        "selected_text": "Hello world",
        "user_intent": "reply",
        "tone": "friendly"
    }
    
    print(f"\n--- {test_name} ---")
    print(f"Testing generate endpoint at {datetime.now()}")
    
    response = requests.post(f"{API_BASE}/generate", json=data, headers=headers)
    
    print(f"Status Code: {response.status_code}")
    print(f"Response: {response.text}")
    
    return response.status_code, response.json() if response.status_code != 500 else response.text

def test_usage_endpoint(token):
    """Test the usage endpoint"""
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    print(f"\n--- Testing Usage Endpoint ---")
    
    response = requests.get(f"{API_BASE}/subscription/usage", headers=headers)
    
    print(f"Status Code: {response.status_code}")
    print(f"Response: {response.text}")
    
    return response.status_code, response.json() if response.status_code == 200 else response.text

def main():
    print("=== ReplyPal Usage Limits Test ===")
    
    # Create test token
    print("Creating test token...")
    token = create_test_token()
    if not token:
        print("Failed to create test token. Exiting.")
        return
    
    print(f"Token created successfully: {token[:50]}...")
    
    # Test usage endpoint first
    test_usage_endpoint(token)
    
    # Test generate endpoint multiple times
    for i in range(1, 8):  # Test 7 times (should exceed free tier limit of 5)
        status_code, response = test_generate_endpoint(token, f"Request {i}")
        
        if status_code == 429:
            print(f"✅ Usage limit correctly enforced at request {i}")
            break
        elif status_code == 200:
            print(f"✅ Request {i} successful")
        else:
            print(f"❌ Unexpected status code {status_code} at request {i}")
        
        # Small delay between requests
        time.sleep(1)
    
    # Test usage endpoint again
    test_usage_endpoint(token)

if __name__ == "__main__":
    main()
